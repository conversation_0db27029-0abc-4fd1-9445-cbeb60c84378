
import pymatgen
from pymatgen.io.vasp.outputs import Outcar
from pymatgen.io.vasp.inputs import Poscar
import numpy as np
import os
import matplotlib.pyplot as plt
import matplotlib as mpl

# 현재 디렉토리를 path로 설정
path = '.'

# OUTCAR 파일 읽기
oc = Outcar(path + '/OUTCAR')

# POSCAR 파일에서 structure 정보 읽기 (OUTCAR에 structure 정보가 없는 경우)
try:
    structure = oc.final_structure
except:
    # POSCAR 파일에서 structure 읽기
    poscar = Poscar.from_file(path + '/POSCAR')
    structure = poscar.structure

# 자기화 정보 추출
mag = [np.abs(oc.magnetization[site_i]['tot']) for site_i in range(len(structure.sites))]

print(f"Number of sites: {len(structure.sites)}")
print(f"Magnetization values: {mag}")