INCAR_PBE+U

! Geometry optimization
### functional PBE+U ###
  GGA      = PE          ! PB, PW(PW86), LM, 91(PW91), PE(PBE), RP(RPBE)
## LDA+U ##
  LMAXMIX  = 4           !for d elements 4
  LDAU     = .TRUE.      ! or .FALSE.
  LDAUTYPE = 2           ! 1(Liechtenstein) or 2(<PERSON><PERSON><PERSON>)
  LDAUL    = 2 2 2      ! l-quantum number on which U acts ((1._q,0._q) for each type
  LDAUU    = 0 3.9 0 ! U coefficient for each species 3.5 for metal in spinel
  LDAUJ    = 0 0 0      ! J coefficient for each species


###spin polarization parameters
  ISPIN    = 2                     ! spin polarized = 2, non spin polarized = 1 
 # MAGMOM   =   ! 


###ionic relaxation
  ISIF     = 3      ! 1 = not change cell shape, 3 = change cell shape
  IBRION   = 2      ! SINGLE POINT = -1, Conjugate gradient = 2, 
  NSW      = 201    ! number of ionic steps taken in minimization. Make it odd. 
  EDIFFG = -0.02 


###electronic relaxation
  ISMEAR   = 0             ! 
  SIGMA = 0.01             ! H
  ENCUT    = 520
  PREC     = med
  ALGO     = Fast          !! electronic minimisation algorithm = 38
  EDIFF    = 3E-6          ! Break condition for the electronic SC loop; 1E-6 for 20atoms 
  NELM     = 200           ! Max no of electronic SC Default = 60
  NELMIN   = 4             ! Min no of electronic SC
  LREAL    = Auto          !! more efficient to use real space projection operators


## File ##
  LWAVE = TRUE     !Whether to wirte the WAVECAR
  LORBIT = 11       !Write DOSCAR and PROCAR
  LCHARG = .TRUE.  !Whether to write the CHG and CHGCAR
 #NEDOS =  ! Number of grid point of DOS

###parallel version
  LPLANE   = .TRUE.
  NPAR     = 4
  LSCALU   = .FALSE.
  NSIM     = 4

